// pages/idPhoto/result/result.js
const idPhotoAPI = require('../../../utils/api/idPhotoAPI');
const app = getApp();

Page({
  data: {
    loading: true,
    loadingText: '正在处理照片...',
    error: false,
    errorMessage: '',
    saving: false,

    // 原始参数
    originalImage: '',
    sizeKey: '',
    sizeName: '',

    // 处理结果
    currentImage: '', // 当前显示的图片（透明背景）
    transparentImage: '', // 透明底图（标准版）
    hdTransparentImage: '', // 透明底图（高清版）

    // 尺寸信息
    sizeInfo: {},

    // 背景色选择和预览
    selectedBackground: 'white',
    backgroundOptions: [], // 从服务器获取的颜色选项
    currentBackgroundStyle: '', // 当前背景样式（CSS）

    // 生成结果信息
    resultInfo: {}
  },

  onLoad(options) {
    console.log('证件照结果页面加载，参数:', options);

    // 解析参数
    const { image, size, sizeName } = options;
    this.setData({
      originalImage: decodeURIComponent(image),
      sizeKey: size,
      sizeName: decodeURIComponent(sizeName || ''),
      sizeInfo: {
        name: decodeURIComponent(sizeName || ''),
        size: size
      }
    });

    // 加载颜色选项并开始处理照片
    this.loadColorsAndProcess();
  },

  /**
   * 加载颜色选项并开始处理照片
   */
  async loadColorsAndProcess() {
    try {
      this.setData({
        loading: true,
        loadingText: '正在加载配置...'
      });

      // 获取颜色列表
      const colorsResult = await idPhotoAPI.getColors();
      console.log('颜色列表:', colorsResult);

      if (colorsResult.success) {
        const formattedColors = colorsResult.data.colors.map(this.formatColorOption);

        this.setData({
          backgroundOptions: formattedColors
        });

        console.log('颜色配置加载完成:', this.data.backgroundOptions.length, '个选项');

        // 开始处理照片
        this.processPhoto();
      } else {
        throw new Error('获取颜色配置失败');
      }

    } catch (error) {
      console.error('加载配置失败:', error);
      this.setData({
        loading: false,
        error: true,
        errorMessage: error.message || '加载配置失败，请重试'
      });
    }
  },

  /**
   * 处理照片 - 优化版本：直接生成透明背景照片
   */
  async processPhoto() {
    try {
      this.setData({
        loading: true,
        loadingText: '正在生成证件照...',
        error: false
      });

      // 调用新的分步处理API，第一步：生成透明背景证件照
      const result = await idPhotoAPI.generateTransparentPhoto({
        imagePath: this.data.originalImage,
        size: this.data.sizeKey
      });

      console.log('透明背景证件照生成结果:', result.success ? '成功' : '失败');

      if (result.success) {
        // 确保base64数据有正确的前缀
        const formatBase64 = (base64Data) => {
          if (!base64Data) return '';
          if (base64Data.startsWith('data:')) return base64Data;
          return `data:image/jpeg;base64,${base64Data}`;
        };

        // 透明背景照片作为主要显示图片
        const transparentImage = formatBase64(result.data.imageBase64);
        const hdTransparentImage = formatBase64(result.data.hdImageBase64);

        console.log('透明背景图片数据准备完成');

        // 设置透明背景图片和基本信息
        this.setData({
          transparentImage: transparentImage, // 透明背景图片
          hdTransparentImage: hdTransparentImage, // 高清透明背景图片
          currentImage: transparentImage, // 当前显示透明背景
          resultInfo: {
            size: result.data.size,
            sizeName: result.data.sizeName,
            color: 'transparent',
            colorName: '透明',
            dimensions: result.data.dimensions
          },
          sizeInfo: {
            name: result.data.sizeName,
            size: result.data.size,
            dimensions: result.data.dimensions
          },
          selectedBackground: 'white', // 默认选择白色背景预览
          loading: false
        });

        console.log('透明背景照片生成完成，尺寸:', this.data.resultInfo.dimensions);

        // 延迟设置默认白色背景预览，确保backgroundOptions已加载
        setTimeout(() => {
          this.updateBackgroundPreview('white');
        }, 100);

      } else {
        throw new Error(result.message || '生成证件照失败');
      }

    } catch (error) {
      console.error('处理照片失败:', error);

      // 上报证件照处理失败错误
      app.reportError('idphoto_process_error', error, {
        page: 'idPhoto/result',
        action: 'processPhoto',
        size_key: this.data.sizeKey,
        original_image: this.data.originalImage ? 'has_image' : 'no_image'
      });
      this.setData({
        loading: false,
        error: true,
        errorMessage: error.message || '处理失败，请重试'
      });
    }
  },

  /** 生成微信端颜色填充样式  */
  formatColorOption(colorOption) {
    if (colorOption.value === 'transparent') {
      return {
        ...colorOption,
        cssStyle: 'background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%); background-size: 20rpx 20rpx; background-position: 0 0, 0 10rpx, 10rpx -10rpx, -10rpx 0rpx;',
      };      
    } else if (colorOption.render === 1) {
      const lightColor = this.lightenColor(colorOption.hex, 60);
      return {
        ...colorOption,
        cssStyle: `background: linear-gradient(to bottom, #${colorOption.hex}, #${lightColor});`,
      };
    } else {
      return {
        ...colorOption,
        cssStyle: `background-color: #${colorOption.hex};`
      };
    }
  },


  /**
   * 更新背景预览样式
   */
  updateBackgroundPreview(colorKey) {
    console.log('更新背景预览:', colorKey, '可用颜色:', this.data.backgroundOptions.length);

    const colorOption = this.data.backgroundOptions.find(option => option.value === colorKey);
    if (!colorOption) {
      console.warn('未找到颜色配置:', colorKey, '可用选项:', this.data.backgroundOptions.map(o => o.value));
      // 如果找不到颜色配置，使用默认白色
      if (colorKey === 'white') {
        this.setData({
          currentBackgroundStyle: 'background-color: #FFFFFF;'
        });
      }
      return;
    }

    let backgroundStyle = '';

    if (colorKey === 'transparent') {
      // 透明背景使用棋盘格图案表示
      backgroundStyle = 'background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%); background-size: 20rpx 20rpx; background-position: 0 0, 0 10rpx, 10rpx -10rpx, -10rpx 0rpx;';
    } else if (colorOption.render === 1) {
      // 渐变背景（垂直渐变：从上往下由深变浅）
      const lightColor = this.lightenColor(colorOption.hex, 60); // 增大渐变幅度到40%
      backgroundStyle = `background: linear-gradient(to bottom, #${colorOption.hex}, #${lightColor});`;
    } else {
      // 纯色背景
      backgroundStyle = `background-color: #${colorOption.hex};`;
    }

    this.setData({
      currentBackgroundStyle: backgroundStyle
    });

    console.log('背景预览样式更新完成:', colorKey, backgroundStyle);
  },

  /**
   * 颜色加亮工具函数
   */
  lightenColor(hex, percent) {
    const num = parseInt(hex, 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;
    return (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
  },

  /**
   * 获取颜色方块的样式
   */
  getColorStyle(colorItem) {
    if (colorItem.value === 'transparent') {
      // 透明背景使用棋盘格图案
      return 'background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%); background-size: 16rpx 16rpx; background-position: 0 0, 0 8rpx, 8rpx -8rpx, -8rpx 0rpx;';
    } else if (colorItem.render === 1) {
      // 渐变背景（垂直渐变：从上往下由深变浅）
      const lightColor = this.lightenColor(colorItem.hex, 50); // 方块中使用更大的渐变幅度
      return `background: linear-gradient(to bottom, #${colorItem.hex}, #${lightColor});`;
    } else {
      // 纯色背景
      return `background-color: #${colorItem.hex};`;
    }
  },

  /**
   * 选择背景色 - 优化版本：只更新预览，不请求服务器
   */
  selectBackground(e) {
    const color = e.currentTarget.dataset.color;
    console.log('选择背景色:', color);

    this.setData({
      selectedBackground: color
    });

    // 只更新背景预览，不请求服务器
    this.updateBackgroundPreview(color);
  },



  /**
   * 保存到相册 - 优化版本：保存时才生成最终照片
   */
  async saveToAlbum() {
    if (!this.data.transparentImage) {
      wx.showToast({
        title: '重新上传制作',
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({ saving: true });

      // 如果选择的是透明背景，直接保存透明图片
      if (this.data.selectedBackground === 'transparent') {
        await this.saveImageToAlbum(this.data.transparentImage, '标准版');
        if (this.data.hdTransparentImage) {
          await this.saveImageToAlbum(this.data.hdTransparentImage, '高清版');
        }
      } else {
        // 其他颜色使用分步处理：为透明背景照片添加背景色
        console.log('为透明背景照片添加背景色，颜色:', this.data.selectedBackground);

        // 为标准版添加背景色（直接使用base64数据）
        const standardResult = await idPhotoAPI.generateAddColor({
          imageBase64: this.data.transparentImage,
          color: this.data.selectedBackground
        });

        if (standardResult.success) {
          // 确保base64数据有正确的前缀
          const formatBase64 = (base64Data) => {
            if (!base64Data) return '';
            if (base64Data.startsWith('data:')) return base64Data;
            return `data:image/jpeg;base64,${base64Data}`;
          };

          const finalImage = formatBase64(standardResult.data.imageBase64);
          await this.saveImageToAlbum(finalImage, '标准版');

          // 如果有高清版，也添加背景色
          if (this.data.hdTransparentImage) {
            const hdResult = await idPhotoAPI.generateAddColor({
              imageBase64: this.data.hdTransparentImage,
              color: this.data.selectedBackground
            });

            if (hdResult.success) {
              const finalHdImage = formatBase64(hdResult.data.imageBase64);
              await this.saveImageToAlbum(finalHdImage, '高清版');
            }
          }
        } else {
          throw new Error(standardResult.message || '添加背景色失败');
        }
      }

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('保存失败:', error);
      wx.showToast({
        title: '保存失败，请重新制作',
        icon: 'error'
      });
    } finally {
      this.setData({ saving: false });
    }
  },



  /**
   * 保存单张图片到相册
   */
  saveImageToAlbum(imageBase64, version) {
    return new Promise((resolve, reject) => {
      // 将base64转换为临时文件
      const base64Data = imageBase64.replace(/^data:image\/\w+;base64,/, '');
      const buffer = wx.base64ToArrayBuffer(base64Data);

      const fs = wx.getFileSystemManager();
      const fileName = `idphoto_${version}_${Date.now()}.jpg`;
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

      fs.writeFile({
        filePath: filePath,
        data: buffer,
        encoding: 'binary',
        success: () => {
          // 请求保存到相册权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              wx.saveImageToPhotosAlbum({
                filePath: filePath,
                success: resolve,
                fail: reject
              });
            },
            fail: () => {
              // 权限被拒绝，引导用户手动开启
              wx.showModal({
                title: '需要相册权限',
                content: '需要获取您的相册权限，请在设置中开启',
                showCancel: false,
                confirmText: '去设置',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          wx.saveImageToPhotosAlbum({
                            filePath: filePath,
                            success: resolve,
                            fail: reject
                          });
                        } else {
                          // 上报用户拒绝相册权限
                          app.reportError('idphoto_permission_denied', '用户拒绝相册权限', {
                            page: 'idPhoto/result',
                            action: 'saveImageToAlbum',
                            permission: 'scope.writePhotosAlbum'
                          });
                          reject(new Error('用户拒绝授权'));
                        }
                      }
                    });
                  } else {
                    // 上报用户拒绝打开设置
                    app.reportError('idphoto_setting_denied', '用户拒绝打开设置', {
                      page: 'idPhoto/result',
                      action: 'saveImageToAlbum',
                      step: 'refuse_open_setting'
                    });
                    reject(new Error('用户拒绝授权'));
                  }
                }
              });
            }
          });
        },
        fail: reject
      });
    });
  },



  /**
   * 重试处理
   */
  retryProcess() {
    this.processPhoto();
  },

  /**
   * 返回
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 图片加载成功
   */
  onImageLoad(e) {
    console.log('图片加载成功:', e.detail);
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
    console.error('图片加载失败:', e.detail);
    console.error('当前图片src:', this.data.currentImage);
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
  }
});
